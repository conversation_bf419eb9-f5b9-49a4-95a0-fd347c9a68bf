diff --git a/node_modules/metro/src/index.flow.js b/node_modules/metro/src/index.flow.js
index b6aa228..9b562fd 100644
--- a/node_modules/metro/src/index.flow.js
+++ b/node_modules/metro/src/index.flow.js
@@ -143,7 +143,9 @@ exports.runServer = async (
     watch,
   });
   for (const handler of unstable_extraMiddleware ?? []) {
-    serverApp.use(handler);
+    if (handler && typeof handler === 'function') {
+      serverApp.use(handler);
+    }
   }
   serverApp.use(middleware);
   let httpServer;
