diff --git a/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.h b/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.h
new file mode 100644
index 0000000..0f56e50
--- /dev/null
+++ b/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.h
@@ -0,0 +1,20 @@
+/*
+ * Copyright (c) Meta Platforms, Inc. and affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ */
+
+#import <Foundation/Foundation.h>
+
+@protocol RCTComponentViewProtocol;
+
+NS_ASSUME_NONNULL_BEGIN
+
+@interface RCTThirdPartyFabricComponentsProvider : NSObject
+
++ (NSDictionary<NSString *, Class<RCTComponentViewProtocol>> *)thirdPartyFabricComponents;
+
+@end
+
+NS_ASSUME_NONNULL_END
diff --git a/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.mm b/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.mm
new file mode 100644
index 0000000..7984d61
--- /dev/null
+++ b/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.mm
@@ -0,0 +1,19 @@
+/*
+ * Copyright (c) Meta Platforms, Inc. and affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ */
+
+#import "RCTThirdPartyFabricComponentsProvider.h"
+
+#import <React/RCTComponentViewFactory.h>
+
+@implementation RCTThirdPartyFabricComponentsProvider
+
++ (NSDictionary<NSString *, Class<RCTComponentViewProtocol>> *)thirdPartyFabricComponents
+{
+  return @{};
+}
+
+@end
