// src/components/redux/orderSlice.ts
import {createSlice, type PayloadAction} from '@reduxjs/toolkit';

// Simple UUID v4 generator that works reliably in React Native
const generateUUID = (): string => {
  // Fallback to Math.random() based UUID generation
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

// Define types for the state
interface OrderState {
  orderType: 'delivery' | 'pickup'; // Only these two possible values
  branchId: string;
  businessId: string;
  logo: string;
  username: string;
  currency: string;
  banners: string;
  allBranches: string;
  selectedBranch: string;
  uniqueOrderId: string;
  cart: any[]; // Ideally, you should type the cart items (e.g., CartItem[])
  allCategories: any[]; // Same for categories
  allItems: any[]; // Same for items
}

const initialState: OrderState = {
  orderType: 'delivery',
  branchId: '',
  businessId: '18',
  // businessId: "12866",
  logo: '',
  username: '',
  currency: '',
  banners: '',
  allBranches: '',
  selectedBranch: '',
  uniqueOrderId: '',
  cart: [], // Initialize cart state to store the cart data
  allCategories: [],
  allItems: [],
};

const orderSlice = createSlice({
  name: 'order',
  initialState,
  reducers: {
    setOrderType: (state, action: PayloadAction<'delivery' | 'pickup'>) => {
      console.log('setOrderType action dispatched:', action.payload);
      state.orderType = action.payload;
    },
    setBranchId: (state, action: PayloadAction<string>) => {
      console.log('setBranch action dispatched:', action.payload);
      state.branchId = action.payload;
    },
    setUniqueOrderId: state => {
      if (!state.uniqueOrderId) {
        // Generate a unique order ID using simple UUID generation
        state.uniqueOrderId = generateUUID();
        console.log('uuid:', state.uniqueOrderId);
      }
    },
    resetUniqueOrderId: state => {
      // Generate a unique order ID using simple UUID generation
      state.uniqueOrderId = generateUUID();
      console.log('uuid:', state.uniqueOrderId);
    },
    setBusinessId: (state, action: PayloadAction<string>) => {
      console.log('setBusiness:', action.payload);
      state.businessId = action.payload;
    },
    setCurrency: (state, action: PayloadAction<string>) => {
      console.log('setCurrency action dispatched:', action.payload);
      state.currency = action.payload;
    },
    setUsername: (state, action: PayloadAction<string>) => {
      console.log('setuser action dispatched:', action.payload);
      state.username = action.payload;
    },
    setLogo: (state, action: PayloadAction<string>) => {
      console.log('logo action dispatched:', action.payload);
      state.logo = action.payload;
    },
    setBanner: (state, action: PayloadAction<string>) => {
      console.log('banner action dispatched:', action.payload);
      state.banners = action.payload;
    },
    setAllBranches: (state, action: PayloadAction<string>) => {
      console.log('all branches dispatched:', action.payload);
      state.allBranches = action.payload;
    },
    setSelectedBranch: (state, action: PayloadAction<any>) => {
      console.log('selected dispatched:', action.payload);
      state.selectedBranch = action.payload;
    },
    setAllCategories: (state, action: PayloadAction<any[]>) => {
      console.log('all branches dispatched:', action.payload);
      state.allCategories = action.payload;
    },
    setAllItems: (state, action: PayloadAction<any[]>) => {
      console.log('all branches dispatched:', action.payload);
      state.allItems = action.payload;
    },
    setCartData: (state, action: PayloadAction<any[]>) => {
      state.cart = action.payload;
    },
    resetCartData: state => {
      state.cart = [];
    },
  },
});

export const {
  setOrderType,
  setBranchId,
  setCartData,
  setBusinessId,
  setCurrency,
  setUsername,
  setLogo,
  setBanner,
  setAllBranches,
  setSelectedBranch,
  setAllCategories,
  setAllItems,
  setUniqueOrderId,
  resetCartData,
  resetUniqueOrderId,
  setBusinessHours,
} = orderSlice.actions;

export default orderSlice.reducer;
