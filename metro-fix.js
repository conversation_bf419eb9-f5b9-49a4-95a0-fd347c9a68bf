// Temporary fix for metro connect middleware issue
const originalConnect = require('connect');

// Patch the connect.use method to handle undefined middleware
const originalUse = originalConnect.prototype.use;
originalConnect.prototype.use = function(route, handle) {
  // If only one argument is provided, it's the handle
  if (arguments.length === 1) {
    handle = route;
    route = '/';
  }
  
  // Skip undefined or null middleware
  if (!handle) {
    console.warn('Skipping undefined middleware');
    return this;
  }
  
  // Call the original use method
  return originalUse.call(this, route, handle);
};

module.exports = originalConnect;
