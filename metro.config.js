const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

// Apply fix for connect middleware issue
require('./metro-fix');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
  resolver: {
    // Add support for additional file extensions
    assetExts: ['bin', 'txt', 'jpg', 'png', 'json', 'gif', 'webp', 'svg'],
    sourceExts: ['js', 'jsx', 'ts', 'tsx', 'json'],
  },
  transformer: {
    // Ensure proper transformation of TypeScript files
    babelTransformerPath: require.resolve(
      '@react-native/metro-babel-transformer',
    ),
  },
  server: {
    // Fix for connect middleware issue
    enhanceMiddleware: (middleware, metroServer) => {
      return (req, res, next) => {
        // Ensure middleware is properly defined before calling
        if (middleware && typeof middleware === 'function') {
          return middleware(req, res, next);
        }
        next();
      };
    },
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
