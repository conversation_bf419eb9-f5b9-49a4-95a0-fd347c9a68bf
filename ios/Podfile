require_relative '../node_modules/react-native/scripts/react_native_pods'

platform :ios, '16.0'
install! 'cocoapods', :deterministic_uuids => false

target 'KhanBaba' do
  use_react_native!(
    :path => '../node_modules/react-native',
    :hermes_enabled => true,
    :fabric_enabled => false,
    :new_arch_enabled => false
  )

  target 'KhanBabaTests' do
    inherit! :complete
  end

  post_install do |installer|
    react_native_post_install(installer)
  end
end
