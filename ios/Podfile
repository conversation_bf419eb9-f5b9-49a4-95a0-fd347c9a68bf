require_relative '../node_modules/react-native/scripts/react_native_pods'

platform :ios, '16.0'
install! 'cocoapods', :deterministic_uuids => false

target 'KhanBaba' do
  use_react_native!(
    :path => '../node_modules/react-native',
    :hermes_enabled => true,
    :fabric_enabled => false,
    :new_arch_enabled => false
  )

  # Manually add packages since autolinking is broken in RN 0.76.8
  pod 'RNGestureHandler', :path => '../node_modules/react-native-gesture-handler'
  pod 'RNCAsyncStorage', :path => '../node_modules/@react-native-async-storage/async-storage'
  pod 'RNDeviceInfo', :path => '../node_modules/react-native-device-info'
  pod 'RNCPushNotificationIOS', :path => '../node_modules/@react-native-community/push-notification-ios'
  pod 'RNScreens', :path => '../node_modules/react-native-screens'

  target 'KhanBabaTests' do
    inherit! :complete
  end

  post_install do |installer|
    react_native_post_install(installer)
  end
end
