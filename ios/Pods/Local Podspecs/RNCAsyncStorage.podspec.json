{"name": "RNCAsyncStorage", "version": "2.1.2", "summary": "Asynchronous, persistent, key-value storage system for React Native.", "license": "MIT", "authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/react-native-async-storage/async-storage#readme", "source": {"git": "https://github.com/react-native-async-storage/async-storage.git", "tag": "v2.1.2"}, "source_files": "ios/**/*.{h,m,mm}", "resource_bundles": {"RNCAsyncStorage_resources": "ios/PrivacyInfo.xcprivacy"}, "platforms": {"ios": "9.0", "tvos": "9.2", "osx": "10.14", "visionos": "1.0"}, "dependencies": {"React-Core": []}}