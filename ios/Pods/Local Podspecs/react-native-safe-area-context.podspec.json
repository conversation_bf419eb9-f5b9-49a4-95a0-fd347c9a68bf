{"name": "react-native-safe-area-context", "version": "4.14.1", "summary": "A flexible way to handle safe area, also works on Android and web.", "license": "MIT", "authors": "<PERSON><PERSON> <janic<PERSON>ples<PERSON>@gmail.com>", "homepage": "https://github.com/th3rdwave/react-native-safe-area-context#readme", "platforms": {"ios": "12.4", "osx": "10.15", "tvos": "12.4", "visionos": "1.0"}, "source": {"git": "https://github.com/th3rdwave/react-native-safe-area-context.git", "tag": "v4.14.1"}, "source_files": "ios/**/*.{h,m,mm}", "exclude_files": "ios/Fabric", "dependencies": {"React-Core": []}}