<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>DoubleConversion.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FBLazyVector.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-KhanBaba-KhanBabaTests.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-KhanBaba.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RCT-Folly-RCT-Folly_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RCT-Folly.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RCTDeprecation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RCTRequired.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RCTTypeSafety.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNCAsyncStorage-RNCAsyncStorage_resources.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNCAsyncStorage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNCPushNotificationIOS.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNDeviceInfo-RNDeviceInfoPrivacyInfo.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNDeviceInfo.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNGestureHandler.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-Core-React-Core_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-Core.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-CoreModules.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-Fabric.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-FabricComponents.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-FabricImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-ImageManager.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-Mapbuffer.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-NativeModulesApple.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTActionSheet.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTAnimation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTAppDelegate.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTBlob.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTFabric.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTLinking.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTNetwork.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTSettings.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTText.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTVibration.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RuntimeApple.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RuntimeCore.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RuntimeHermes.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-callinvoker.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-cxxreact-React-cxxreact_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-cxxreact.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-debug.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-defaultsnativemodule.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-domnativemodule.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-featureflags.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-featureflagsnativemodule.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-graphics.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-hermes.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-idlecallbacksnativemodule.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-jserrorhandler.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-jsi.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-jsiexecutor.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-jsinspector.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-jsitracing.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-logger.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-microtasksnativemodule.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-nativeconfig.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-perflogger.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-performancetimeline.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-rendererconsistency.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-rendererdebug.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-rncore.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-runtimeexecutor.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-runtimescheduler.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-timing.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-utils.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>ReactCodegen.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>ReactCommon.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SocketRocket.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Yoga.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>boost-boost_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>boost.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>fmt.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>glog-glog_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>glog.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>hermes-engine.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict/>
</dict>
</plist>
